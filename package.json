{"name": "memateapp", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/inter": "^5.0.15", "@fontsource/roboto": "^5.0.8", "@hello-pangea/dnd": "^16.6.0", "@hookform/resolvers": "^3.9.0", "@mui/icons-material": "^5.14.18", "@mui/material": "^5.16.0", "@mui/x-date-pickers": "^6.18.6", "@stripe/react-stripe-js": "^2.8.1", "@stripe/stripe-js": "^4.9.0", "@szhsin/react-menu": "^4.2.1", "@tanstack/react-query": "^5.51.1", "@uidotdev/usehooks": "^2.4.1", "array-move": "^4.0.0", "axios": "^1.6.2", "bootstrap": "^5.3.2", "bootstrap-icons": "^1.11.2", "chart.js": "^4.4.7", "chartjs-plugin-annotation": "^3.1.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "concurrently": "^9.1.0", "cross-env": "^7.0.3", "date-fns": "^2.25.0", "dayjs": "^1.11.10", "draft-js": "^0.11.7", "emoji-picker-react": "^4.12.2", "google-libphonenumber": "^3.2.40", "immutable": "^4.3.7", "leaflet": "^1.9.4", "libphonenumber-js": "^1.12.9", "nanoid": "^5.0.7", "npm": "^10.8.3", "nprogress": "^0.2.0", "primereact": "^10.8.2", "quill": "^2.0.2", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.9.1", "react-bootstrap-icons": "^1.10.3", "react-calendly": "^4.3.1", "react-circular-progressbar": "^2.1.0", "react-countup": "^6.5.3", "react-datepicker": "^5.0.0", "react-dom": "^18.2.0", "react-dom-confetti": "^0.2.0", "react-draft-wysiwyg": "^1.15.0", "react-draggable-list": "^4.2.1", "react-dropzone": "^14.2.3", "react-easy-crop": "^5.0.8", "react-error-boundary": "^5.0.0", "react-flatpickr": "^3.10.13", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.52.2", "react-international-phone": "^4.2.6", "react-leaflet": "^4.2.1", "react-password-strength-bar": "^0.4.1", "react-player": "^2.15.1", "react-resizable": "^3.0.5", "react-scripts": "^5.0.0", "react-select": "^5.8.0", "react-slideshow-image": "^4.3.0", "react-sortable-hoc": "^2.0.0", "react-verification-input": "^4.1.1", "sass": "^1.77.8", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "three-dots": "^0.3.2", "use-debounce": "^10.0.2", "wait-on": "^8.0.1", "web-vitals": "^2.1.4", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "DISABLE_ESLINT_PLUGIN=true react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron:serve": "concurrently -k \"cross-env BROWSER=none npm start\" \"npm run electron:start\"", "electron:build": "npm run build && electron-builder -c.extraMetadata.main=build/main.js", "electron:start": "wait-on tcp:3000 && electron .", "lint": "eslint .", "lint:fix": "eslint --fix .", "generate:sitemap": "node generate-sitemap.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-swipeable-views": "^0.13.5", "electron": "^33.2.1", "electron-builder": "^25.1.8", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "globals": "^15.9.0", "loader-utils": "3.2.1", "react-router-dom": "^6.22.1", "sitemap": "^8.0.0", "typescript": "4.4.4"}}