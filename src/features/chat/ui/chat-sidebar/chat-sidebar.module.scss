.chatSidebar {
  flex: 0 0 360px;
  height: 100%;
  display: flex;
  background: #fff;
  align-self: stretch;
  flex-direction: column;
  align-items: flex-start;
  border-right: 1px solid var(--gray-200, #eaecf0);
  overflow: hidden;
}

.sidebarHeader {
  width: 100%;
  display: flex;
  align-items: center;
  align-self: stretch;
  flex-direction: column;
}

.headerFirstRow {
  width: 100%;
  display: flex;
  padding: 20px 24px;
  align-items: center;
  justify-content: space-between;
}

.unreadCount {
  gap: 4px;
  display: flex;
  padding: 2px 6px;
  border-radius: 6px;
  background: #fff;
  height: fit-content;
  align-items: center;
  border: 1px solid var(--Gray-300, #d0d5dd);

  span {
    position: relative;
    text-align: center;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
    color: var(--Gray-700, #344054);
  }
}

.sidebarTitle {
  color: var(--Gray-900, #101828);
  font-family: Inter;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px;
  margin: 0px;
}

.archivedLabel {
  margin: 0px;
  color: var(--Gray-700, #344054);
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  letter-spacing: 0.175px;
}

.headerSecondRow {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0px 24px 12px 24px;
  justify-content: space-between;
}

.headerThirdRow {
  display: flex;
  padding: 4px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border: 1px solid var(--gray-200, #eaecf0);
  background: var(--gray-25, #fcfcfd);
}

.tabButton {
  display: flex;
  height: 36px;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;

  span {
    color: var(--gray-500, #667085);
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
  }

  div {
    display: flex;
    padding: 2px 8px;
    align-items: center;
    border-radius: 16px;
    border: 1px solid var(--gray-200, #eaecf0);
    background: var(--gray-50, #f9fafb);
    color: var(--gray-700, #344054);
    text-align: center;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    letter-spacing: 0.15px;
  }

  &.active {
    background: #fff;
    box-shadow: 0px 1px 3px 0px rgba(16, 24, 40, 0.1),
      0px 1px 2px 0px rgba(16, 24, 40, 0.06);

    span {
      color: var(--gray-700, #344054);
    }
  }
}

.listContainer {
  width: 100%;
  height: 100%;
  overflow: auto;
  border-right: 1px solid var(--gray-200, #eaecf0);
}
