.userList {
  width: 100%;
  overflow-y: auto;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.userItem {
  width: 100%;
  display: flex;
  padding: 12px 16px;
  align-self: stretch;
  border-bottom: 1px solid var(--gray-200, #eaecf0);
  background: #fff;
  text-decoration: none;
  transition: background-color 0.2s ease;
  cursor: pointer;

  &:hover {
    background: var(--gray-50, #f9fafb);
  }

  &:active {
    background: var(--primary-25, #f2faff);
  }
}

.userItem.active {
  background: var(--primary-25, #f2faff);
}

.userItemContent {
  display: flex;
  width: 100%;
  align-items: center;
  gap: 12px;
}

.userAvatarWrapper {
  position: relative;
  flex-shrink: 0;
}

.onlineIndicator {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #12b76a;
  border: 1.5px solid white;
  bottom: 0;
  left: 28px;
  z-index: 1;
}

.userAvatar {
  display: flex;
  width: 40px;
  height: 40px;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  border: 1px solid var(--gray-200, #eaecf0);
  background: var(--gray-50, #f9fafb);
  color: var(--gray-700, #344054);
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
  overflow: hidden;
  text-transform: uppercase;
}

.userInfo {
  flex: 1;
  min-width: 0;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  align-items: flex-start;
}

.userName {
  color: var(--gray-900, #101828);
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  margin-bottom: 2px;
}

.lastMessage {
  color: var(--gray-600, #475467);
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.lastMessageTime {
  color: var(--gray-600, #475467);
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  flex-shrink: 0;
  align-self: flex-start;
}
