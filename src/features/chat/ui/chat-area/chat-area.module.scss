.chatArea {
  flex: 1 0 0;
  height: 100%;
  display: flex;
  align-items: center;
  align-self: stretch;
  flex-direction: column;
  background-color: #fff;
}

.chatContent {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.messagesContainer {
  flex: 1;
  gap: 16px;
  height: 100%;
  padding: 16px;
  display: flex;
  overflow-y: auto;
  position: relative;
  flex-direction: column;
}

.loadingContainer {
  top: 0px;
  left: 0px;
  width: 100%;
  padding-top: 15px;
  text-align: center;
  position: absolute;
}

.messageInputContainer {
  display: flex;
  padding: 16px;
  align-items: center;
  gap: 12px;
  align-self: stretch;
  border-top: 1px solid var(--gray-200, #eaecf0);
  margin-top: auto;
  background-color: #fff;
}

.messageInput {
  outline: none !important;
  padding: 10px 80px 10px 16px !important;
  font-size: 14px !important;
  border-radius: 8px !important;
  width: 100% !important;
  border-color: var(--gray-300, #d0d5dd) !important;
  height: 44px !important;
  box-shadow: none !important;

  &:focus {
    outline: none !important;
    box-shadow: none !important;
  }
}

.messageInputIcons {
  gap: 8px;
  top: 50%;
  right: 12px;
  display: flex;
  position: absolute;
  align-items: center;
  transform: translateY(-50%);
}

.attachButton,
.emojiButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--gray-100, #f2f4f7);
  }

  &:active {
    background-color: var(--gray-200, #eaecf0);
  }
}

.sendButton {
  background-color: #158ecc !important;
  border-color: #158ecc !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 10px 16px !important;
  border-radius: 40px !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  height: 44px !important;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05) !important;

  &:hover {
    background-color: #158ecc !important;
    box-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1) !important;
  }

  &:active {
    background-color: #158ecc !important;
    box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05) !important;
  }

  &:disabled {
    background-color: #158ecc !important;
    border-color: #158ecc !important;
    box-shadow: none !important;
    opacity: 0.5 !important;
  }

  i {
    font-size: 16px !important;
    margin-left: 4px !important;
  }
}

.emptyChat {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  gap: 24px;
}

.emptyChatIcon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.emptyChatText {
  color: var(--gray-700, #344054);
  text-align: center;
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
}

.chatMenu {
  position: absolute;
  top: 60px;
  right: 16px;
  z-index: 1000;
  background: white;
  border-radius: 8px;
  box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03),
    0px 12px 16px -4px rgba(16, 24, 40, 0.08);

  :global(.p-menu-list) {
    padding: 8px 0;
  }

  :global(.p-menuitem) {
    margin: 0;
  }

  :global(.p-menuitem-link) {
    padding: 8px 16px;
    color: var(--gray-700, #344054);
    font-size: 14px;

    &:hover {
      background-color: var(--gray-50, #f9fafb);
    }
  }
}
