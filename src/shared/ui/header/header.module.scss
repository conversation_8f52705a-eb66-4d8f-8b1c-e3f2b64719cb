.trialNote {
  top: 0;
  width: 100%;
  min-height: 30px;
  color: #fff;
  display: flex;
  font-size: 14px;
  justify-content: center;
  align-items: center;
  position: sticky;
  background: #1ab2ff;
  position: relative;
}

.subscriptionFailedNote {
  top: 0;
  width: 100%;
  min-height: 30px;
  color: #fff;
  display: flex;
  font-size: 14px;
  justify-content: center;
  align-items: center;
  position: sticky;
  background: #ff584a;
  position: relative;
}

.navbarActionIcon {
  width: 40px;
  height: 40px;
  cursor: pointer;
  border-radius: 6px;
  align-items: center;
  margin: 0px !important;
  justify-content: center;
  display: flex !important;
}

.navbarActionIcon:hover {
  background: var(--Primary-25, #F2FAFF);
}

// Variables (assuming these are defined elsewhere, adjust as needed)
$colorG1: var(--colorG1);
$colorG2: var(--colorG2);
$colorG3: var(--colorG3);

// Base styles
.menuItem {
  ul {
    li {
      display: inline-block;
      vertical-align: middle;

      &:nth-child(2) {
        margin: 0 40px;
      }

      a {
        padding: 9px 15px;
        color: $colorG1;
        text-align: center;
        border-radius: 6px;
        display: flex;
        align-items: center;

        img {
          margin-right: 12px;
        }
      }
    }
  }

  text-align: center;
}

.headerTop {
  padding: 12px 13px;
  position: relative;
  z-index: 999;

  &.business {
    .menuActive.managementMain {
      background: #f2faff;
      color: #158ecc;
    }
  }

  &.work {
    .menuActive.managementMain1 {
      background: #f2faff;
      color: #158ecc;
    }
  }
}

.companyLogo {
  display: flex;
  align-items: center;
  color: $colorG1;
  text-align: center;
  letter-spacing: -0.16px;
  border-radius: var(--radius-xs, 4px);
  border: 0.5px solid var(--Gray-100, #f2f4f7);

  img {
    height: 40px;
  }

  span {
    position: relative;
    padding-left: 32px;

    &:before,
    &:after {
      position: absolute;
      content: "";
      background: $colorG2;
      width: 1px;
      height: 38px;
      top: -5px;
    }

    &:before {
      left: 16px;
    }

    &:after {
      right: -16px;
    }
  }
}

.selectOptionHead {
  margin-left: 16px;
  min-width: 196px;

  :global {

    .css-13cymwt-control,
    .css-t3ipsp-control {
      border: 0 !important;
      box-shadow: none !important;

      &:focus {
        border: 0 !important;
        box-shadow: none !important;
      }
    }

    .css-1dimb5e-singleValue span {
      display: inline-block;
      width: 131px;
      white-space: nowrap;
      overflow: hidden !important;
      text-overflow: ellipsis;
      color: #101828;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
    }

    .css-1u9des2-indicatorSeparator {
      display: none;
    }
  }
}

.headerNav {
  padding: 4px 21px;

  ul {
    &.left li:first-child {
      padding-left: 0;
    }

    &.right li:last-child {
      padding-right: 0;
    }

    li {
      display: inline-block;
      vertical-align: middle;
      margin: 0 2px;

      a {
        padding: 10px 20px;
        border-radius: 6px;
      }
    }
  }
}

// Menu states
.managementMain {
  &:hover {
    background: #e6f4fd;
    color: #158ecc;
  }
}

@mixin menuState($color, $gradient) {
  color: $color;
  background: $gradient;
}

@mixin menuHover($color, $gradient) {
  &:hover {
    color: $color;
    background: $gradient;
  }
}

// Menu items
.sales {
  @include menuHover(#6fbaff, linear-gradient(135deg, #89f7fe1a, #66a6ff1a));

  &.menuActive {
    @include menuState(#6fbaff, linear-gradient(135deg, #89f7fe1a, #66a6ff1a));
  }
}

.management {
  @include menuHover(#fe8890,
    linear-gradient(135deg, rgba(255, 211, 165, 0.1), rgba(253, 101, 133, 0.1)));

  &.menuActive {
    @include menuState(#fe8890,
      linear-gradient(135deg,
        rgba(255, 211, 165, 0.1),
        rgba(253, 101, 133, 0.1)));
  }
}

.clients {
  @include menuHover(#222,
    linear-gradient(135deg, rgba(114, 237, 242, 0.1), rgba(81, 81, 229, 0.1)));

  &.menuActive {
    @include menuState(#5c83ea,
      linear-gradient(135deg, rgba(114, 237, 242, 0.1), rgba(81, 81, 229, 0.1)));
  }
}

.expense {
  @include menuHover(#f96a94,
    linear-gradient(135deg, rgba(247, 79, 172, 0.1), rgba(252, 178, 79, 0.1)));

  &.menuActive {
    @include menuState(#f96a94,
      linear-gradient(135deg, rgba(247, 79, 172, 0.1), rgba(252, 178, 79, 0.1)));
  }
}

.invoices {
  @include menuHover(#a8d1e7,
    linear-gradient(0deg, rgba(132, 250, 176, 0.1), rgba(172, 203, 238, 0.1)));

  &.menuActive {
    @include menuState(#4fc182,
      linear-gradient(0deg, rgba(132, 250, 176, 0.1), rgba(172, 203, 238, 0.1)));
  }
}

.orders {
  @include menuHover(#508c9e,
    linear-gradient(45deg, rgba(74, 135, 154, 0.1), rgba(197, 237, 245, 0.1)));

  &.menuActive {
    @include menuState(#508c9e,
      linear-gradient(45deg, rgba(74, 135, 154, 0.1), rgba(197, 237, 245, 0.1)));
  }
}

.statistics {
  @include menuHover(#ff8508,
    linear-gradient(45deg, rgba(255, 122, 0, 0.1), rgba(255, 212, 57, 0.1)));

  &.menuActive {
    @include menuState(#ff8508,
      linear-gradient(45deg, rgba(255, 122, 0, 0.1), rgba(255, 212, 57, 0.1)));
  }
}

.suppliers {
  @include menuHover(#8149d5,
    linear-gradient(0deg, rgba(155, 35, 234, 0.1), rgba(95, 114, 189, 0.1)));

  &.menuActive {
    @include menuState(#8149d5,
      linear-gradient(0deg, rgba(155, 35, 234, 0.1), rgba(95, 114, 189, 0.1)));
  }
}

.tasks {
  @include menuHover(#d16cb6,
    linear-gradient(45deg, rgba(255, 157, 228, 0.1), rgba(255, 234, 246, 0.1)));

  &.menuActive {
    @include menuState(#d16cb6,
      linear-gradient(45deg, rgba(255, 157, 228, 0.1), rgba(255, 234, 246, 0.1)));
  }
}

.news {
  @include menuHover(#9890e3,
    linear-gradient(45deg, #8fefff54, rgba(255, 234, 246, 0.1)));

  &.menuActive {
    @include menuState(#9890e3,
      linear-gradient(45deg, #8fefff54, rgba(255, 234, 246, 0.1)));
  }
}

.approval {
  @include menuHover(#78997c,
    linear-gradient(0deg, rgba(158, 194, 162, 0.1), rgba(192, 227, 189, 0.1)));

  &.menuActive {
    @include menuState(#78997c,
      linear-gradient(0deg, rgba(158, 194, 162, 0.1), rgba(192, 227, 189, 0.1)));
  }
}

.dashboard {
  @include menuHover(#6da7bd,
    linear-gradient(270deg, rgba(116, 235, 213, 0.1), rgba(159, 172, 230, 0.1)));

  &.menuActive {
    @include menuState(#6da7bd,
      linear-gradient(270deg,
        rgba(116, 235, 213, 0.1),
        rgba(159, 172, 230, 0.1)));
  }
}

.jobs {
  @include menuHover(#ffb800,
    linear-gradient(45deg, rgba(255, 184, 0, 0.1), rgba(255, 245, 0, 0.1)));

  &.menuActive {
    @include menuState(#ffb800,
      linear-gradient(45deg, rgba(255, 184, 0, 0.1), rgba(255, 245, 0, 0.1)));
  }
}

.people {
  @include menuHover(#a66daa,
    linear-gradient(135deg, rgba(255, 245, 195, 0.1), rgba(148, 82, 165, 0.1)));

  &.menuActive {
    @include menuState(#a66daa,
      linear-gradient(135deg, rgba(255, 245, 195, 0.1), rgba(148, 82, 165, 0.1)));
  }
}

// Avatar styles
.avatarWrap {
  &:hover {
    .userImageBox {
      border-radius: 200px;
      border: 1px solid var(--Primary-200, #a3e0ff);
      background: linear-gradient(180deg, #f9fafb 0%, #e4f6ff 100%), #fff;
      color: var(--Primary-600, #158ecc);
    }

    .avatarInfo {
      color: var(--Primary-600, #0a4766);

      span {
        color: var(--Primary-600, #158ecc);
      }
    }
  }

  ul {
    margin-right: 32px;
    position: relative;

    &:before {
      position: absolute;
      content: "";
      background: $colorG2;
      width: 1px;
      height: 38px;
      top: -5px;
      right: -10px;
    }

    li {
      display: inline-block;
      list-style: none;
      margin: 0 12px;
    }
  }
}

.avatarInfo {
  text-align: left;
  color: $colorG1;

  span {
    display: block;
    color: $colorG3;
    font-weight: 400;
  }
}

.userImageBox {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 200px;
  border: 1px solid var(--Gray-200, #eaecf0);
  background: linear-gradient(180deg, #f9fafb 0%, #edf0f3 100%), #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  color: var(--Gray-600, #475467);
  font-family: Roboto;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.25px;
}

.headerLocationWrapper {
  border-radius: 4px;
  border: 1px solid var(--Gray-100, #f2f4f7);
  background: var(--Gray-25, #fcfcfd);
  padding: 2px 9px;
  height: 40px;

  h6 {
    color: var(--Gray-800, #1d2939);
    font-family: Inter;
    font-size: 16px;
    font-weight: 400;
    line-height: 14px;
    letter-spacing: -0.16px;
    margin: 0;
  }

  :global {
    .css-1fdsijx-ValueContainer {
      padding: 0;
    }

    .css-13cymwt-control,
    .css-t3ipsp-control {
      background-color: transparent !important;
      border-width: 0 !important;
      min-height: auto !important;
    }

    .css-1xc3v61-indicatorContainer,
    .css-15lsz6c-indicatorContainer {
      padding: 0;
      margin-top: -18px;
    }

    .css-qbdosj-Input {
      padding: 0 !important;
      margin: 0 !important;
    }
  }

  &:hover {
    border: 1px solid var(--Primary-50, #ebf8ff);
    background: var(--Primary-25, #f2faff);

    :global(.css-1hb7zxy-IndicatorsContainer svg) {
      fill: #48c1ff;
    }
  }
}

// Media queries
@media (min-width: 1280px) and (max-width: 1500px) {
  .headerNav {
    ul li a img {
      margin-right: 7px;
    }
  }
}