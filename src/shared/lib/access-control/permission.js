export const PERMISSIONS = Object.freeze({
    CLIENTS: {
        CREATE: 'CREATE_CLIENT',
        READ: 'READ_CLIENT',
        UPDATE: 'UPDATE_CLIENT',
        DELETE: 'DELETE_CLIENT',
    },
    SUPPLIERS: {
        CREATE: 'CREATE_SUPPLIER',
        READ: 'READ_SUPPLIER',
        UPDATE: 'UPDATE_SUPPLIER',
        DELETE: 'DELETE_SUPPLIER',
    },
    SALES: {},
    MANAGEMENT: {},
    PROJECTS: {},
    STATISTIC: {},
    EXPENSE: {
        CREATE: 'CREATE_EXPENSE',
        READ: 'READ_EXPENSE',
        UPDATE: 'UPDATE_EXPENSE',
        DELETE: 'DELETE_EXPENSE',
    },
    INVOICE: {
        CREATE: 'CREATE_INVOICE',
        READ: 'READ_INVOICE',
        UPDATE: 'UPDATE_INVOICE',
        DELETE: 'DELETE_INVOICE',
    },
    SETTINGS: {
        SUBSCRIPTION: {
            BUY_BUSINESS_SUBSCRIPTION: 'BUY_BUSINESS_SUBSCRIPTION',
            CANCEL_BUSINESS_SUBSCRIPTION: 'CANCEL_BUSINESS_SUBSCRIPTION',
            BUY_COMPANY_USER_SUBSCRIPTION: 'BUY_COMPANY_USER_SUBSCRIPTION',
            BUY_WORK_SUBSCRIPTION: 'BUY_WORK_SUBSCRIPTION',
            CANCEL_WORK_SUBSCRIPTION: 'CANCEL_WORK_SUBSCRIPTION',
            ACTIVE_WORK_SUBSCRIPTION: 'ACTIVE_WORK_SUBSCRIPTION',
            BUY_WORK_USER_SUBSCRIPTION: 'BUY_WORK_USER_SUBSCRIPTION',
            BUY_LOCATION_SUBSCRIPTION: 'BUY_LOCATION_SUBSCRIPTION',
            REMOVE_LOCATION_SUBSCRIPTION: 'REMOVE_LOCATION_SUBSCRIPTION',
        },
        USERS: {
            MOBILE_APP: {
                ADD: 'ADD_USER',
                READ: 'READ_USER',
                UPDATE: 'UPDATE_USER',
                DISCONNECT_USER: 'DISCONNECT_USER',
                RECONNECT_USER: 'RECONNECT_USER',
                SHOW_DISCONNECTED_USERS: 'SHOW_DISCONNECTED_USERS',
            },
            DESKTOP: {
                ADD: 'ADD_USER',
                READ: 'READ_USER',
                DELETE: 'DELETE_USER',
                UPDATE: 'UPDATE_USER',
                RESTORE: 'RESTORE_USER',
                SHOW_DELETED_USERS: 'SHOW_DELETED_USERS',
            },
        },
        LOCATION: {
            ADD: 'ADD_LOCATION',
            READ: 'READ_LOCATION',
            UPDATE: 'UPDATE_LOCATION',
            DELETE: 'DELETE_LOCATION'
        },
    }
});
