.signatureEditor {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  min-height: 399px;
  margin-bottom: 24px;
}

.editorToolbar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 0;
  margin-bottom: 16px;
}

.viewToggle {
  display: flex;
  gap: 8px;
}

.viewButton, .pasteButton {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  border-radius: 6px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(16, 24, 40, 0.1);
  }
}

.pasteArea {
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
  z-index: 10;
}

.pasteInstructions {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;

  p {
    margin-bottom: 16px;
    font-size: 14px;
    color: #344054;
    font-weight: 500;
  }
}

.pasteTarget {
  min-height: 100px;
  padding: 12px;
  border: 1px dashed #D0D5DD;
  border-radius: 4px;
  background-color: #F9FAFB;
  margin-bottom: 12px;
  cursor: text;
  font-size: 14px;
  color: #667085;
  display: flex;
  align-items: center;
  justify-content: center;

  &:focus {
    outline: none;
    border-color: #1AB2FF;
    background-color: #fff;
  }
}

.pasteActions {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 16px;
}

.editorContainer {
  display: flex;
  flex: 1;
  overflow: hidden;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.editorSide {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.previewSide {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.editorHeader, .previewHeader {
  padding: 10px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #344054;
  }
}

.editorControls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.htmlLabel {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #667085;
  background-color: #F2F4F7;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.previewPane {
  padding: 16px;
  flex: 1;
  background-color: #fff;
  overflow: auto;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.previewPlaceholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 20px;
}

.previewPlaceholderContent {
  max-width: 300px;
  text-align: center;

  h6 {
    margin: 16px 0 8px;
    font-size: 16px;
    font-weight: 500;
    color: #344054;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #667085;
  }
}

.previewPlaceholderIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  margin: 0 auto;
  background-color: #F9FAFB;
  border-radius: 50%;

  svg {
    color: #D0D5DD;
  }
}

.codePane {
  flex: 1;
  border: none;
  border-radius: 0;
  resize: none;
  font-family: monospace;
  font-size: 14px;
  padding: 16px;
  background-color: #f8f9fa;
  color: #344054;
  line-height: 1.5;

  &:focus {
    outline: none;
    box-shadow: none;
    background-color: #fff;
  }

  &::placeholder {
    color: #98A2B3;
  }
}

/* Email signature specific styles */
.previewPane :global(.email-signature) {
  font-family: Arial, sans-serif;
  max-width: 500px;
  line-height: 1.4;
  width: 100%;
}

.previewPane :global(table) {
  border-collapse: collapse;
}

.previewPane :global(td) {
  padding: 0;
}

.previewPane :global(h3) {
  margin: 0 0 5px 0;
  font-weight: 600;
}

.previewPane :global(p) {
  margin: 0 0 5px 0;
}

.previewPane :global(a) {
  text-decoration: none;
}

.previewPane :global(img) {
  border: none;
  display: inline-block;
}
