.formSection {
  h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--Gray-900, #101828);
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--Gray-200, #EAECF0);
  }
}

.previewSection {
  position: sticky;
  top: 24px;
  align-self: flex-start;
}

.templateSelector {
  margin-bottom: 24px;

  .templateOptions {
    display: flex;
    overflow-x: auto;
    gap: 16px;
    padding: 8px 0;
    scrollbar-width: thin;
    scrollbar-color: var(--Gray-300, #D0D5DD) transparent;

    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--Gray-300, #D0D5DD);
      border-radius: 6px;
    }

    .templateOption {
      flex: 0 0 120px;
      border: 2px solid transparent;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.2s;
      box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(16, 24, 40, 0.1);
      }

      &.selected {
        border-color: var(--Primary-600, #0E85C7);
        box-shadow: 0 0 0 4px rgba(14, 133, 199, 0.1);
      }

      .thumbnailContainer {
        width: 100%;
        height: 80px;
        border-radius: 4px 4px 0 0;
        overflow: hidden;
      }

      .templateName {
        font-size: 12px;
        text-align: center;
        padding: 8px;
        background-color: var(--Gray-50, #F9FAFB);
        color: var(--Gray-700, #344054);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-top: 1px solid var(--Gray-200, #EAECF0);
      }
    }
  }
}

.imageUploadSection {
  margin-top: 16px;
  margin-bottom: 16px;

  .imageUploadContainer {
    display: flex;
    gap: 16px;
    margin-top: 12px;
  }

  .imageUploadBox {
    width: 120px;
    height: 120px;
    border: 2px dashed var(--Gray-300, #D0D5DD);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      border-color: var(--Primary-300, #7CD4FD);
      background-color: var(--Primary-25, #F2FAFF);
    }

    .uploadIcon {
      color: var(--Gray-400, #98A2B3);
      margin-bottom: 8px;
    }

    p {
      font-size: 12px;
      color: var(--Gray-500, #667085);
      margin: 0;
      text-align: center;
    }

    &.hasImage {
      border: none;
      padding: 0;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
      }

      .removeImageButton {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 24px;
        height: 24px;
        border-radius: 12px;
        background-color: white;
        border: 1px solid var(--Gray-200, #EAECF0);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &:hover {
          background-color: var(--Error-50, #FEF3F2);
          border-color: var(--Error-300, #FDA29B);
          color: var(--Error-600, #D92D20);
        }
      }
    }
  }
}

.socialMediaSection {
  margin-top: 16px;
  margin-bottom: 16px;

  .socialMediaList {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 12px;
  }

  .socialMediaItem {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background-color: var(--Gray-50, #F9FAFB);
    border-radius: 6px;
    border: 1px solid var(--Gray-200, #EAECF0);
    cursor: pointer;

    .socialIcon {
      color: var(--Gray-500, #667085);
    }

    &.active {
      background-color: var(--Primary-50, #EBF8FF);
      border-color: var(--Primary-200, #A3E0FF);

      .socialIcon {
        color: var(--Primary-600, #0E85C7);
      }
    }
  }
}

.previewCard {
  border: 1px solid var(--Gray-200, #EAECF0);
  border-radius: 8px;
  overflow: hidden;
  background-color: white;

  .previewHeader {
    padding: 16px;
    background-color: var(--Gray-50, #F9FAFB);
    border-bottom: 1px solid var(--Gray-200, #EAECF0);

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: var(--Gray-900, #101828);
      margin: 0;
    }
  }

  .previewBody {
    padding: 24px;

    .emailPreview {
      padding: 16px;
      border: 1px solid var(--Gray-200, #EAECF0);
      border-radius: 8px;
      background-color: white;

      .emailHeader {
        padding-bottom: 16px;
        border-bottom: 1px solid var(--Gray-200, #EAECF0);
        margin-bottom: 16px;

        .emailHeaderItem {
          font-size: 12px;
          color: var(--Gray-600, #475467);
          margin-bottom: 4px;

          strong {
            color: var(--Gray-700, #344054);
          }
        }
      }

      .emailBody {
        font-size: 14px;
        color: var(--Gray-700, #344054);
        line-height: 1.5;

        p {
          margin-bottom: 12px;
        }
      }

      .emailSignature {
        margin-top: 24px;
        padding-top: 16px;
        border-top: 1px solid var(--Gray-200, #EAECF0);
      }
    }
  }

  .compatibilityInfo {
    padding: 16px;
    background-color: var(--Gray-50, #F9FAFB);
    border-top: 1px solid var(--Gray-200, #EAECF0);

    h4 {
      font-size: 14px;
      font-weight: 600;
      color: var(--Gray-700, #344054);
      margin-bottom: 8px;
    }

    .compatibilityList {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .compatibilityItem {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: var(--Gray-600, #475467);

        .compatibilityIcon {
          color: var(--Success-500, #12B76A);
        }
      }
    }
  }
}

.label {
  display: block;
  font-size: 14px;
  color: #475467;
  font-weight: 500;
  line-height: 22px;
  font-family: Inter;
  font-style: normal;
  letter-spacing: .14px;
  color: var(--Gray-600, #475467);
}

.inputBox {
  background: #fff;
  font-family: Inter;
  border-radius: 4px;
  padding: 10px 14px;
  outline: none !important;
  border: 1px solid #d0d5dd;
  box-shadow: none !important;
  color: #101828 !important;
}

.textarea {
  width: 100%;
  background: #fff;
  font-family: Inter;
  border-radius: 4px;
  padding: 10px 14px;
  outline: none !important;
  border: 1px solid #d0d5dd;
  box-shadow: none !important;
  color: #101828 !important;
  resize: auto;
  min-height: 440px;
}