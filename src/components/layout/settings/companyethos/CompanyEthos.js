import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTrialHeight } from '../../../../app/providers/trial-height-provider';
import ComingSoon from '../../../../shared/ui/coming-soon';

const CompanyEthos = () => {
    const { trialHeight } = useTrialHeight();

    return (
        <>
            <Helmet>
                <title>MeMate - Company Ethos</title>
            </Helmet>
            <div className="settings-content setModalelBoots w-100">
                <div className='headSticky'>
                    <h1>Company Ethos</h1>
                </div>
                <div className={`content_wrap_main d-flex align-items-center w-100`} style={{ height: `calc(100vh - 150px - ${trialHeight}px)` }}>
                    <ComingSoon />
                </div>
            </div>
        </>
    );
};

export default CompanyEthos;
