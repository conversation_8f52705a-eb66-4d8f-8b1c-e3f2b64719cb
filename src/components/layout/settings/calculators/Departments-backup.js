import React, { useEffect, useMemo, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { PlusLg, PencilSquare, ChevronDown, ChevronUp, X, PlusCircle, Save, Backspace, GripVertical } from "react-bootstrap-icons";
import { Helmet } from 'react-helmet-async';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { useQuery } from '@tanstack/react-query';
import clsx from 'clsx';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { Dialog } from 'primereact/dialog';
import { InputNumber } from 'primereact/inputnumber';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Skeleton } from 'primereact/skeleton';
import Button from 'react-bootstrap/Button';
import { toast } from 'sonner';
import style from './calculators.module.scss';
import DeleteConfirmationModal from './delete-confirmation-modal';
import { createCalculator, createDepartment, createSubDepartment, getCalculationByReferenceId, getDepartments, updateCalculator, updateDepartment, updateSubDepartment, reorderDepartments, reorderSubDepartments } from '../../../../APIs/CalApi';
import { formatAUD } from '../../../../shared/lib/format-aud';

const Departments = () => {
    const [visible, setVisible] = useState(false);
    const [visible2, setVisible2] = useState(false);
    const [createCalculatorId, setCreateCalculatorId] = useState(null);
    const [editDepartment, setEditDepartment] = useState({ id: null, name: null });
    const [subDepartment, setSubDepartment] = useState(null);
    const [activeCalculations, setActiveCalculations] = useState({});

    const [AccordionActiveTab, setAccordionActiveTab] = useState(undefined);
    const [AccordionActiveTab2, setAccordionActiveTab2] = useState(undefined);
    
    // State for drag and drop functionality
    const [isDragging, setIsDragging] = useState(false);
    const [draggedDepartmentId, setDraggedDepartmentId] = useState(null);

    const departmentQuery = useQuery({
        queryKey: ['departments'],
        queryFn: () => getDepartments(1),
        enabled: true,
    });

    const refetch = () => {
        departmentQuery.refetch();
    };

    const getCalculator = async (id) => {
        try {
            const data = await getCalculationByReferenceId(id);
            setActiveCalculations(prev => ({ ...prev, [id]: data }));
        } catch (error) {
            console.error('Error fetching calculator:', error);
        }
    };

    const editHandleDepartment = (e, data) => {
        e.preventDefault();
        e.stopPropagation();
        setEditDepartment(data);
        setVisible(true);
    };

    const createSubDepartmentOpen = (e, parent, i) => {
        e.preventDefault();
        e.stopPropagation();
        setSubDepartment({ id: null, parent, name: null });
        setVisible2(true);
    };

    const handleCreateCalculator = (e, id, i) => {
        e.preventDefault();
        e.stopPropagation();
        setCreateCalculatorId(id);
    };

    const updateSubDepartment = (e, id, parent, name) => {
        e.preventDefault();
        e.stopPropagation();

        setSubDepartment({ id, parent, name });
        setVisible2(true);
    };

    // Drag and Drop Handlers
    const handleDragStart = (start) => {
        setIsDragging(true);
        setDraggedDepartmentId(start.draggableId);
    };

    const handleDragEnd = async (result) => {
        setIsDragging(false);
        setDraggedDepartmentId(null);

        const { destination, source, type } = result;

        // Dropped outside the list
        if (!destination) {
            return;
        }

        // No movement
        if (destination.droppableId === source.droppableId && destination.index === source.index) {
            return;
        }

        try {
            if (type === 'DEPARTMENT') {
                // Handle department reordering
                const departments = departmentQuery?.data?.filter((data) => !data?.deleted) || [];
                const reorderedDepartments = Array.from(departments);
                const [movedDepartment] = reorderedDepartments.splice(source.index, 1);
                reorderedDepartments.splice(destination.index, 0, movedDepartment);

                // Update the order in the API
                await reorderDepartments(reorderedDepartments);
                toast.success('Department order updated successfully');
                departmentQuery.refetch();
            } else if (type === 'SUBDEPARTMENT') {
                // Handle sub-department reordering within the same department
                const departmentId = source.droppableId.replace('subdepartment-', '');
                const department = departmentQuery?.data?.find(d => d.id.toString() === departmentId);
                
                if (department) {
                    const subDepartments = department.subindexes?.filter((data) => !data?.deleted) || [];
                    const reorderedSubDepartments = Array.from(subDepartments);
                    const [movedSubDepartment] = reorderedSubDepartments.splice(source.index, 1);
                    reorderedSubDepartments.splice(destination.index, 0, movedSubDepartment);

                    // Update the order in the API
                    await reorderSubDepartments(reorderedSubDepartments);
                    toast.success('Sub-department order updated successfully');
                    departmentQuery.refetch();
                }
            }
        } catch (error) {
            console.error('Error reordering:', error);
            toast.error('Failed to update order. Please try again.');
        }
    };

    return (
        <>
            <Helmet>
                <title>MeMate - Departments</title>
            </Helmet>
            <div className='headSticky'>
                <h1>Calculators</h1>
            </div>
            <div className={clsx(style.wraper, `content_wrap_main`)}>
                <div className='content_wrapper'>
                    <div className="listwrapper">
                        <div className={`topHeadStyle pb-4 ${style.topHeadBorder}`}>
                            <h2>Departments</h2>
                            <button onClick={() => setVisible(true)} className={"outline-button"}>Create Department <PlusLg color="#000000" size={20} className='mb-1 ms-1' /></button>
                        </div>
                        <div>
                            <DragDropContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
                                <Droppable droppableId="departments" type="DEPARTMENT">
                                    {(provided, snapshot) => (
                                        <div
                                            ref={provided.innerRef}
                                            {...provided.droppableProps}
                                            className={snapshot.isDraggingOver ? style.dragOver : ''}
                                        >
                                            <Accordion
                                                activeIndex={AccordionActiveTab}
                                                onTabChange={(e) => setAccordionActiveTab(e.index)}
                                                expandIcon={<div className='expandIcon'>
                                                    <ChevronUp size={16} color='#344054' />
                                                </div>}
                                                collapseIcon={<div className='collapseIcon'>
                                                    <ChevronDown size={16} color='#106B99' />
                                                </div>}
                                                onTabOpen={() => {
                                                    setAccordionActiveTab2(undefined);
                                                }}
                                            >
                                                {
                                                    departmentQuery?.data?.filter((data) => !data?.deleted)?.map((department, i) => {
                                                        const subDepartment = department?.subindexes?.filter((data) => !data?.deleted);
                                                        return (
                                                            <Draggable
                                                                key={`department-${department.id}`}
                                                                draggableId={`department-${department.id}`}
                                                                index={i}
                                                            >
                                                                {(provided, snapshot) => (
                                                                    <div
                                                                        ref={provided.innerRef}
                                                                        {...provided.draggableProps}
                                                                        className={clsx(
                                                                            snapshot.isDragging ? style.dragging : '',
                                                                            isDragging && draggedDepartmentId !== `department-${department.id}` ? style.notDragging : ''
                                                                        )}
                                                                    >
                                                                        <AccordionTab
                                                                            className={clsx(style.accorHeadbox, 'main-accordion-header')}
                                                                            key={department.id}
                                                                            header={
                                                                                <span className="d-flex align-items-center justify-content-between">
                                                                                    <div className='d-flex align-items-center'>
                                                                                        <div {...provided.dragHandleProps} style={{ cursor: 'move', display: 'flex', alignItems: 'center' }}>
                                                                                            <GripVertical color="#98A2B3" size={16} />
                                                                                        </div>
                                                                                        <span className={clsx(style.accorHeadStyle, 'active-header-text ms-2')}>{department.name}</span>
                                                                                        <div className={clsx(style.editIconBox, 'editItem')} onClick={(e) => editHandleDepartment(e, { id: department.id, name: department.name })} style={{ visibility: 'hidden' }}>
                                                                                            <PencilSquare color="#106B99" size={16} />
                                                                                        </div>
                                                                                    </div>
                                                                                    <div className={clsx(style.RItem, 'editItem')} style={{ visibility: 'hidden', marginRight: '14px' }}>
                                                                                        <DeleteConfirmationModal title={"Department"} api={`/settings/departments/delete/${department.id}/`} refetch={departmentQuery.refetch} />
                                                                                        <Button className={style.create} onClick={(e) => createSubDepartmentOpen(e, department.id, i)}><PlusLg color="#106B99" size={18} className='me-2' />Product / Service</Button>
                                                                                    </div>
                                                                                </span>
                                                                            }
                                                                        >
                                                                            {/* Sub-departments with drag and drop */}
                                                                            <Accordion
                                                                                activeIndex={AccordionActiveTab2}
                                                                                onTabChange={(e) => setAccordionActiveTab2(e.index)}
                                                                                className='innnerAccordian'
                                                                                expandIcon={<div className={clsx(style.innerExpandIcon)}>
                                                                                    <ChevronUp size={16} color='#344054' />
                                                                                </div>}
                                                                                collapseIcon={<div className={clsx(style.innerCollapseIcon)}>
                                                                                    <ChevronDown size={16} color='#106B99' />
                                                                                </div>}
                                                                                onTabOpen={(e) => {
                                                                                    const subindexId = subDepartment[e.index].id;
                                                                                    getCalculator(subindexId);
                                                                                }}
                                                                                onTabClose={() => {
                                                                                    return false;
                                                                                }}
                                                                            >
                                                                                <Droppable droppableId={`subdepartment-${department.id}`} type="SUBDEPARTMENT">
                                                                                    {(provided, snapshot) => (
                                                                                        <div
                                                                                            ref={provided.innerRef}
                                                                                            {...provided.droppableProps}
                                                                                            className={snapshot.isDraggingOver ? style.subDragOver : ''}
                                                                                        >
                                                                                            {
                                                                                                subDepartment?.map((subindex, i) => (
                                                                                                    <Draggable
                                                                                                        key={`subdepartment-${subindex.id}`}
                                                                                                        draggableId={`subdepartment-${subindex.id}`}
                                                                                                        index={i}
                                                                                                    >
                                                                                                        {(provided, snapshot) => (
                                                                                                            <div
                                                                                                                ref={provided.innerRef}
                                                                                                                {...provided.draggableProps}
                                                                                                                className={clsx(
                                                                                                                    snapshot.isDragging ? style.subDragging : '',
                                                                                                                    isDragging && draggedDepartmentId !== `subdepartment-${subindex.id}` ? style.subNotDragging : ''
                                                                                                                )}
                                                                                                            >
                                                                                                                <AccordionTab
                                                                                                                    className={clsx(style.innerBoxStyle, style.innerAccordionTab)}
                                                                                                                    key={subindex.id}
                                                                                                                    header={(
                                                                                                                        <span className="d-flex align-items-center justify-content-between">
                                                                                                                            <div className='d-flex align-items-center'>
                                                                                                                                <div {...provided.dragHandleProps} style={{ cursor: 'move', position: 'relative', top: '2px', left: '-40px', display: 'flex', alignItems: 'center' }}>
                                                                                                                                    <GripVertical color="#98A2B3" size={16} />
                                                                                                                                </div>
                                                                                                                                <span className={clsx(style.accorHeadStyle, 'active-header-text')}>{subindex.name}</span>
                                                                                                                                <div className={clsx(style.editIconBox2, 'editItem')} onClick={(e) => updateSubDepartment(e, subindex.id, department.id, subindex.name)} style={{ visibility: 'hidden' }}>
                                                                                                                                    <PencilSquare color="#106B99" size={16} />
                                                                                                                                </div>
                                                                                                                            </div>

                                                                                                                            <div className={clsx(style.RItem, 'editItem')} style={{ visibility: 'hidden' }}>
                                                                                                                                <DeleteConfirmationModal title={"Product / Service "} api={`/settings/sub-departments/delete/${subindex.id}/`} refetch={departmentQuery.refetch} />
                                                                                                                                <Button className={style.create} onClick={(e) => handleCreateCalculator(e, subindex.id, i)}><PlusLg color="#106B99" size={18} className='me-2' />Create Calculator</Button>
                                                                                                                            </div>
                                                                                                                        </span>
                                                                                                                    )}
                                                                                                                >
                                                                                                                    {/* Calculator content */}
                                                                                                                    {
                                                                                                                        activeCalculations[subindex.id] ? (
                                                                                                                            <ViewCalculators index={subindex.id}
                                                                                                                                isNewCreate={createCalculatorId === subindex.id}
                                                                                                                                cancelCreateCalculator={setCreateCalculatorId}
                                                                                                                                refetch={getCalculator}
                                                                                                                                calculators={activeCalculations[subindex.id]}
                                                                                                                                name={subindex.name}
                                                                                                                            />
                                                                                                                        ) : <LoadingCalculator />
                                                                                                                    }
                                                                                                                </AccordionTab>
                                                                                                            </div>
                                                                                                        )}
                                                                                                    </Draggable>
                                                                                                ))
                                                                                            }
                                                                                            {provided.placeholder}
                                                                                        </div>
                                                                                    )}
                                                                                </Droppable>
                                                                            </Accordion>
                                                                        </AccordionTab>
                                                                    </div>
                                                                )}
                                                            </Draggable>
                                                        );
                                                    })
                                                }
                                                {provided.placeholder}
                                            </Accordion>
                                        </div>
                                    )}
                                </Droppable>
                            </DragDropContext>
                        </div>
                    </div>
                </div>
            </div>
            <CreateDepartment visible={visible} setVisible={setVisible} refetch={departmentQuery.refetch} editDepartment={editDepartment} setEditDepartment={setEditDepartment} />
            <CreateSubDepartmentModal visible2={visible2} setVisible2={setVisible2} refetch={departmentQuery.refetch} editSubDepartment={subDepartment} setEditSubDepartment={setSubDepartment} />
        </>
    );
};

export default Departments;
