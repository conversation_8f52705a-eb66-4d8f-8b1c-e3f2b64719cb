.leftSection {
  display: flex;
  align-items: center;
  padding: 0px 100px;
}

.topHeading {
  top: 152px;
  position: relative;
}

.heading {
  color: var(--Gray-900, #101828);
  text-align: center;

  /* Display md/Semibold */
  font-family: Inter;
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: 44px; /* 122.222% */
  letter-spacing: -0.72px;
}

.subHeading {
  color: var(--Gray-600, #475467);
  text-align: center;
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%; /* 24px */
  letter-spacing: 0.16px;
}

.payButton {
  color: #fff;
  display: flex;
  height: 44px;
  padding: 10px 18px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 40px;
  position: relative;
  top: -150px;
  border: 1px solid var(--Success-600, #079455);
  background: var(--Success-600, #079455);
}

.rightSection {
  height: 100dvh;
  display: flex;
  overflow: hidden;
  position: relative;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #1ab2ff 7.86%, #ffb258 94.98%);
}

.rightTextBox {
  display: flex;
  padding: 24px;
  width: 588px;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
  position: relative;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.rightTextBox::after {
  content: "";
  width: 80px;
  height: 80px;
  left: -25.382px;
  bottom: -24px;
  position: absolute;
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.rightTextBox::before {
  content: "";
  width: 72px;
  height: 72px;
  position: absolute;
  right: -32px;
  top: -31.561px;
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.rightSectionContent::before {
  content: "";
  width: 32px;
  height: 32px;
  position: absolute;
  right: -65px;
  top: 0.439px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.rightSectionContent {
  position: relative;
  border-radius: 10px;
  background-color: #fff;
}

.rightSectionTextContent {
  padding: 16px 24px 24px 24px;
}
.rightSectionText {
  color: var(--Gray-600, #475467);
  text-align: center;
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%; /* 24px */
  letter-spacing: 0.16px;
}
