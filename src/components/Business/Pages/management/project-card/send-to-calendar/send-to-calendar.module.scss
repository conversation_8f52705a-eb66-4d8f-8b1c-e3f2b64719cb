.calendarButton {
  background-color: #ffffff !important;
  color: #344054 !important;
  border: 1px solid #d0d5dd !important;
  border-radius: 40px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  margin-right: 10px;

  &:hover {
    background-color: #ffffff !important;
    color: #344054 !important;
  }

  &:focus,
  &:active {
    background-color: #ffffff !important;
    color: #344054 !important;
  }

  img {
    width: 16px;
    height: 16px;
  }
}

.calendarModal {
  :global(.modal-content) {
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Fix for calendar dropdown appearing behind modal */
  :global(.p-datepicker) {
    z-index: 1056 !important; /* Bootstrap modal has z-index 1055 */
  }

  :global(.modal-header) {
    border-bottom: 1px solid #f2f4f7;
    padding: 16px 24px;

    :global(.modal-title) {
      font-size: 18px;
      font-weight: 600;
      color: #101828;
    }
  }

  :global(.modal-body) {
    padding: 24px;
  }

  :global(.form-label) {
    font-weight: 500;
    color: #344054;
    margin-bottom: 6px;
  }

  :global(.form-control),
  :global(.form-select) {
    border-color: #d0d5dd;
    border-radius: 6px;
    padding: 10px 14px;

    &:focus {
      border-color: #0086c9;
      box-shadow: 0 0 0 0.25rem rgba(0, 134, 201, 0.25);
    }
  }

  :global(.form-check-input:checked) {
    background-color: #0086c9;
    border-color: #0086c9;
  }
}

.calendarTypeRadio {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;

  label {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid #d0d5dd;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f9fafb;
    }

    &.selected {
      background-color: #f0f9ff;
      border-color: #0086c9;
    }

    input {
      margin-right: 8px;
    }
  }
}

.modalTitle {
  font-size: 18px;
  font-weight: 600;
  color: #101828;
  padding: 8px 10px;
}

.formLabel {
  color: #475467;
  color: var(--Gray-600, #475467);
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  letter-spacing: 0.14px;
  line-height: 22px;
}

.AutoComplete ul {
  width: 100%;
  padding: 4px 10px 4px 10px;
  outline: none !important;
  box-shadow: none !important;
  border: 1px solid var(--Border, #d0d5dd);
  display: flex;
  flex-wrap: nowrap;
  overflow: auto;
}

.AutoComplete.borderDanger ul {
  border: 1px solid #dc3545 !important;
}

.AutoComplete.bcc ul {
  padding: 4px 10px 4px 10px;
}

.AutoComplete input {
  min-width: 100px;
}
