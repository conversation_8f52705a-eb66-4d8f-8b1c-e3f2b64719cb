.heading {
  color: var(--Gray-900, #101828);
  font-family: Inter;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 30px;
}

.viewBox {
  display: flex;
  width: 48px;
  height: 48px;
  padding: 6px;
  justify-content: center;
  align-items: center;
  border-radius: 28px;
  border: 8px solid var(--Success-50, #ecfdf3);
  background: var(--Success-100, #dcfae6);
}

.border {
  border: 1px solid #f2f4f7 !important;
}

.borderBottom {
  border-bottom: 1px solid #f2f4f7 !important;
}

.cardBody {
  padding: 12px 24px !important;
}

.jobDetailHeading {
  color: var(--Gray-600, #475467);
  font-family: Inter;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: -0.18px;
}

.customLabel {
  color: var(--Text-Gray, #667085);
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0.14px;
}

.text {
  color: var(--Gray-900, #101828);
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
}

.plannedTable {
  border-collapse: collapse;
  width: 100%;
}

.plannedTable th {
  padding: 8px 16px;
  border-bottom: 1px solid var(--Gray-200, #eaecf0);
}

.plannedTable th:nth-child(2),
.plannedTable td:nth-child(2) {
  border-left: 1px solid var(--Gray-200, #eaecf0);
  border-right: 1px solid var(--Gray-200, #eaecf0);
}

.plannedTable td {
  color: var(--Gray-600, #475467);
  padding: 8px 16px;
  border-bottom: 1px solid var(--Gray-200, #eaecf0) !important;
}

.whiteTr td {
  background: #fff !important;
}

.plannedTable td:nth-child(2),
.plannedTable td:nth-child(3) {
  text-align: right;
}

.plannedTable th.active1 {
  background: var(--Primary-50, #ebf8ff);
  border: 1px solid var(--Primary-300, #76d1ff) !important;
  border-left: 1px solid var(--Primary-300, #76d1ff) !important;
}

.plannedTable th.nonActive {
  border-right: unset !important;
}

.plannedTable td.active1 {
  border-right: 1px solid var(--Primary-300, #76d1ff) !important;
  border-left: 1px solid var(--Primary-300, #76d1ff) !important;
}

.plannedTable td.nonActive {
  border-right: unset !important;
}

.plannedTable td.active1.borderRightNone {
  border-right: none !important;
}

.plannedTable td.active1.active2 {
  background: var(--Primary-50, #ebf8ff);
}

.plannedTable td.active3 {
  border-right: 1px solid var(--Primary-300, #76d1ff) !important;
}

.lastRow td {
  border-bottom: none !important;
}

.fixRateBox {
  display: flex;
  padding: 4px 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  background: #e1fc9d;
  color: var(--Gray-800, #1d2939);

  /* Title group */
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 142.857% */
  letter-spacing: 0.21px;
  white-space: nowrap;
}

.shiftBox {
  display: flex;
  padding: 4px 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  border: 1px solid var(--Gray-300, #d0d5dd);
  background: #fff;
  color: var(--Gray-800, #1d2939);

  /* Title group */
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 142.857% */
  letter-spacing: 0.21px;
}

.clockIcon {
  display: flex;
  padding: 6px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  border: 1px solid var(--Gray-300, #d0d5dd);
  background: var(--Gray-25, #fcfcfd);
}

.moneyBox {
  margin: auto;
  width: fit-content;
  color: var(--Gray-700, #344054);
  text-align: center;

  /* Body large */
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
  letter-spacing: -0.08px;
  display: flex;
  padding: 2px 6px;
  align-items: center;
  border-radius: 4px;
  border: 1px solid var(--Gray-300, #d0d5dd);
  background: var(--Base-White, #fff);
}

.bonusBox {
  color: var(--Primary-600, #158ecc);
  border: 1px solid var(--Primary-300, #76d1ff);
  background: var(--Primary-50, #ebf8ff);
}

.deductionBox {
  color: var(--Error-600, #b42318);
  border: 1px solid var(--Error-300, #fda29b);
  background: var(--Error-50, #fef3f2);
}

.InputTextarea {
  width: 100% !important;
  border-radius: 4px !important;
  border: 1px solid var(--Gray-300, #d0d5dd) !important;
  background: var(--Base-White, #fff) !important;
  box-shadow: none !important;
  outline: none !important;
  resize: none !important;
}

.activeOutlineButton,
.activeOutlineButton:hover,
.activeOutlineButton:focus {
  color: #fff !important;
  background: var(--Primary-600, #158ecc) !important;
  border: 1px solid var(--Primary-600, #158ecc) !important;
}

.amountRow {
  gap: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.dollar {
  font-size: 1rem;
  padding: 0 0.25rem;
}

.helpIcon {
  font-size: 1rem;
  color: #9e9e9e;
  cursor: help;
  padding-left: 0.5rem;
}

.buttonGroup {
  display: flex;
  gap: 0.25rem;
}

.iconButton {
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  border: none;
  background-color: #e3f2fd;
  color: #1976d2;
  font-size: 1.2rem;
  line-height: 1;
  cursor: pointer;
}

.amountInputBox {
  position: relative;
  display: flex;
  padding: 0px 14px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: 6px;
}

.bonusInputBox {
  border: 1px solid var(--Primary-300, #76d1ff);
  background: var(--Base-White, #fff);

  .inputText {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    border-left: 1px solid var(--Primary-300, #76d1ff);
  }
}

.deductionInputBox {
  border: 1px solid var(--Error-300, #fda29b);
  background: var(--Base-White, #fff);

  .inputText {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    border-left: 1px solid var(--Error-300, #fda29b);
  }
}

.feedbackLabel {
  color: var(--Gray-700, #344054);
  text-align: center;

  /* Body large */
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
  letter-spacing: -0.08px;
  margin-bottom: 15px;
}

.chooseUserBox {
  width: 460px;
  padding: 12px 16px;
  border-radius: 4px;
  border: 1px solid var(--Gray-200, #eaecf0);
  background: #fff;
}

.type {
  display: flex;
  align-items: center;
  justify-content: center;
}

.shift {
  color: var(--Basic-Dark-Basic, #191c1f);
  text-align: center;

  /* Body small */
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
  letter-spacing: 0.12px;
  display: flex;
  padding: 0px 6px 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 100px 0px 0px 100px;
  border-top: 1px solid var(--statuses-types-light-green, #dafd90);
  border-bottom: 1px solid var(--statuses-types-light-green, #dafd90);
  border-left: 1px solid var(--statuses-types-light-green, #dafd90);
  background: var(--statuses-types-light-green, #dafd90);
}

.fix,
.hours {
  color: var(--Basic-Dark-Basic, #191c1f);
  text-align: center;

  /* Body small */
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
  letter-spacing: 0.12px;
  display: flex;
  padding: 0px 8px 0px 6px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 0px 100px 100px 0px;
  border: 1px solid var(--statuses-types-light-green, #dafd90);
  background: #fff;
}

.timeFrame {
  color: var(--Basic-Dark-Basic, #191c1f);
  text-align: center;

  /* Body small */
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
  letter-spacing: 0.12px;
  display: flex;
  padding: 0px 6px 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 100px 0px 0px 100px;
  border-top: 1px solid var(--statuses-types-light-yellow, #faf48d);
  border-bottom: 1px solid var(--statuses-types-light-yellow, #faf48d);
  border-left: 1px solid var(--statuses-types-light-yellow, #faf48d);
  background: var(--statuses-types-light-yellow, #faf48d);
}

.timeTracker {
  color: var(--Basic-Dark-Basic, #191c1f);
  text-align: center;

  /* Body small */
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
  letter-spacing: 0.12px;
  display: flex;
  padding: 0px 6px 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 100px 0px 0px 100px;
  border-top: 1px solid #eecbff;
  border-bottom: 1px solid #eecbff;
  border-left: 1px solid #eecbff;
  background: var(--statuses-types-light-purple, #eecbff);
}

.timeFrame2 {
  color: var(--Basic-Dark-Basic, #191c1f);
  text-align: center;

  /* Body small */
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
  letter-spacing: 0.12px;
  display: flex;
  padding: 0px 8px 0px 6px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 0px 100px 100px 0px;
  border: 1px solid var(--statuses-types-light-yellow, #eecbff);
  background: #fff;
}

.historyTitle {
  color: var(--Text-Dark-Gray, #344054);

/* Body large bold */
font-family: Inter;
font-size: 16px;
font-style: normal;
font-weight: 600;
line-height: 24px; /* 150% */
letter-spacing: 0.08px;
margin-bottom: 0px;
}

.historySubtitle {
  color: var(--Gray-400, #98A2B3);

/* Body super small */
font-family: Inter;
font-size: 10px;
font-style: normal;
font-weight: 400;
line-height: 22px; /* 220% */
letter-spacing: 0.1px;
margin-bottom: 0px;
}