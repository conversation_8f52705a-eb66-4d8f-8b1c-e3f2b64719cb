.filterBox {
  display: flex;
  padding: var(--spacing-sm, 2px) var(--spacing-md, 2px);
  justify-content: center;
  align-items: center;
  gap: var(--spacing-md, 8px);
  border-radius: var(--radius-xs, 4px);
  border: 1px solid var(--Gray-300, #d0d5dd);
  background: #fff;
  height: 32px;
  width: 32px;
  border-radius: 50%;
}

.total {
  color: var(--Gray-600, #475467);
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
  letter-spacing: 0.08px;
}

.tabButton {
  color: var(--Gray-700, #344054);

  /* Body default */
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  letter-spacing: 0.14px;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
}

.activeReviewApprove {
  color: var(--Success-500, #17b26a);
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0.14px;
  background: var(--Success-50, #ecfdf3);
}

.activeApproved {
  color: var(--Primary-700, #106b99);
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0.14px;
  border-radius: 6px;
  background: var(--Primary-50, #ebf8ff);
}

.totalCount {
  color: #ffc301;
  text-align: center;
  display: flex;
  padding: var(--spacing-xxs, 2px) var(--spacing-md, 8px);
  align-items: center;
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
  letter-spacing: 0.12px;
  border-radius: 16px;
  border: 1px solid #fff0c1;
  background: #fffaeb;
}

/* Jobs table css */
.shadowRight {
  box-shadow: 0 0 6px #dedede;
  clip-path: inset(0px -6px 0px 0px);
}

.payment {
  display: flex;
  min-width: 47px;
  padding: var(--spacing-xxs, 2px) var(--spacing-sm, 6px);
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-xs, 4px);
}

.paymentHours {
  color: var(--Orange-700, #ac7535);
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid var(--Orange-300, #ffd19b);
  background: var(--Orange-25, #fff7ee);
}

.paymentFix {
  color: var(--Gray-700, #344054);
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid var(--Gray-300, #d0d5dd);
  background: var(--Gray-25, #fcfcfd);
}

.paymentTracker {
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: var(--Gray-700, #344054);
  background: var(--Primary-50, #ebf8ff);
  border: 1px solid var(--Primary-200, #a3e0ff);
}

.time {
  color: var(--Primary-700, #106b99);
  text-align: center;

  /* Text xs/Medium */
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 18px; /* 150% */
  display: flex;
  padding: var(--spacing-xxs, 2px) var(--spacing-md, 8px)
    var(--spacing-xxs, 2px) var(--spacing-sm, 6px);
  align-items: center;
  gap: var(--spacing-xs, 4px);
  border-radius: 16px;
  border: 1px solid var(--Primary-200, #a3e0ff);
  background: var(--Primary-50, #ebf8ff);
}

.time.frame {
  color: var(--Success-700, #067647);
  text-align: center;
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 18px;
  border-radius: 16px;
  border: 1px solid var(--Success-200, #a9efc5);
  background: var(--Success-50, #ecfdf3);
}

.clientImg {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  overflow: hidden;
  margin-right: 10px;
  border-radius: 34px;
  border: 0.75px solid #ccc;
  background: linear-gradient(180deg, #f9fafb 0%, #edf0f3 100%);
}

.clientName {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  font-size: 12px;
  overflow: hidden;
  margin-right: 10px;
  border-radius: 34px;
  border: 0.75px solid #ccc;
  background: linear-gradient(180deg, #f9fafb 0%, #edf0f3 100%);
}

.inProgress {
  font-size: 12px;
  padding: 2px 6px;
  line-height: 18px;
  font-weight: 500;
  color: var(--Warning-700, #b54708);
  border-radius: 16px;
  border: 1px solid var(--Warning-200, #fedf89);
  background: var(--Warning-50, #fffaeb);
  //
}

.approved {
  font-size: 12px;
  padding: 2px 6px;
  line-height: 18px;
  font-weight: 500;
  color: var(--Success-700, #067647);
  border-radius: 16px;
  border: 1px solid var(--Success-200, #a9efc5);
  background: var(--Success-50, #ecfdf3);
}

.defaultStatus {
  font-size: 12px;
  padding: 2px 6px;
  line-height: 18px;
  font-weight: 500;
  color: #344054;
  border-radius: 16px;
  border: 1px solid #eaecf0;
  background: #f9fafb;
}

.finished {
  font-size: 12px;
  padding: 2px 6px;
  line-height: 18px;
  font-weight: 500;
  color: #6941c6;
  border-radius: 16px;
  border: 1px solid #e9d7fe;
  background: #f9fafb;
}

.linkToProjectCard {
  color: var(--Gray-400, #98a2b3);

  /* Body default */
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  letter-spacing: 0.14px;
}

tr:hover .linkToProjectCard {
  color: var(--Primary-600, #158ecc);

  /* Body default */
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  letter-spacing: 0.14px;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}

.finishedAction {
  color: var(--Gray-800, #1d2939);

  /* Body small */
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
  letter-spacing: 0.12px;
  display: flex;
  padding: 6px 12px;
  justify-content: center;
  align-items: center;
  gap: 6px;
  border-radius: 4px;
  border: 1px solid var(--Gray-300, #d0d5dd);
  background: var(--Base-White, #fff);
}

tr:hover .finishedAction {
  border-radius: 4px;
  border: 1px solid var(--Success-300, #75e0a7);
  background: var(--Success-50, #ecfdf3);
}

.assign {
  font-size: 12px;
  padding: 2px 6px;
  line-height: 18px;
  font-weight: 500;
  color: var(--Error-700, #b42318);
  border-radius: 16px;
  border: 1px solid var(--Error-200, #fecdca);
  background: var(--Error-50, #fef3f2);
}

.assignAction {
  font-size: 12px;
  padding: 4px 6px;
  line-height: 18px;
  font-weight: 500;
  color: var(--Error-700, #b42318);
  border-radius: 4px;
  border: 1px solid var(--Error-200, #fecdca);
  background: var(--Error-50, #fef3f2);
}

.shadowLeft {
  box-shadow: 0 0 6px #dedede;
  clip-path: inset(0px 0px 0px -6px);
}

.yearDropdown {
  &:hover,
  &:focus,
  &:active {
    background-color: #f9fafb !important;
    box-shadow: none !important;
  }

  &::after {
    display: none !important;
  }
}

.clientPhoto,
.workerPhoto {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.button,
.button:hover,
.button:focus,
.button:active {
  color: var(--Gray-700, #344054);
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  padding: 8px 14px !important;
  border-radius: 40px;
  border: 1px solid var(--Gray-300, #d0d5dd);
  background: var(--Base-White, #fff);
}

.type {
  display: flex;
  align-items: center;
  justify-content: center;
}

.shift {
  color: var(--Basic-Dark-Basic, #191c1f);
  text-align: center;

  /* Body small */
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
  letter-spacing: 0.12px;
  display: flex;
  padding: 0px 6px 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 100px 0px 0px 100px;
  border-top: 1px solid var(--statuses-types-light-green, #dafd90);
  border-bottom: 1px solid var(--statuses-types-light-green, #dafd90);
  border-left: 1px solid var(--statuses-types-light-green, #dafd90);
  background: var(--statuses-types-light-green, #dafd90);
}

.fix,
.hours {
  color: var(--Basic-Dark-Basic, #191c1f);
  text-align: center;

  /* Body small */
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
  letter-spacing: 0.12px;
  display: flex;
  padding: 0px 8px 0px 6px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 0px 100px 100px 0px;
  border: 1px solid var(--statuses-types-light-green, #dafd90);
  background: #fff;
}

.timeFrame {
  color: var(--Basic-Dark-Basic, #191c1f);
  text-align: center;

  /* Body small */
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
  letter-spacing: 0.12px;
  display: flex;
  padding: 0px 6px 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 100px 0px 0px 100px;
  border-top: 1px solid var(--statuses-types-light-yellow, #faf48d);
  border-bottom: 1px solid var(--statuses-types-light-yellow, #faf48d);
  border-left: 1px solid var(--statuses-types-light-yellow, #faf48d);
  background: var(--statuses-types-light-yellow, #faf48d);
}

.timeTracker {
  color: var(--Basic-Dark-Basic, #191c1f);
  text-align: center;

  /* Body small */
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
  letter-spacing: 0.12px;
  display: flex;
  padding: 0px 6px 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 100px 0px 0px 100px;
  border-top: 1px solid #eecbff;
  border-bottom: 1px solid #eecbff;
  border-left: 1px solid #eecbff;
  background: var(--statuses-types-light-purple, #eecbff);
}

.timeFrame2 {
  color: var(--Basic-Dark-Basic, #191c1f);
  text-align: center;

  /* Body small */
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 183.333% */
  letter-spacing: 0.12px;
  display: flex;
  padding: 0px 8px 0px 6px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 0px 100px 100px 0px;
  border: 1px solid var(--statuses-types-light-yellow, #eecbff);
  background: #fff;
}

.totalStyle {
  color: var(--Gray-800, #1d2939);
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  letter-spacing: 0.175px;
  margin-bottom: 0px;
}

.verticalTop {
  vertical-align: baseline !important;
}