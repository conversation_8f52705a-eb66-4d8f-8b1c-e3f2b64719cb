# See https://help.github.com/articles/ignoring-files/ for more about ignoring files

# ───────────────────────────────
# Node.js dependencies
# ───────────────────────────────
node_modules/
.pnp/
.pnp.js

# ───────────────────────────────
# Build output
# ───────────────────────────────
dist/
build/

# ───────────────────────────────
# Testing and analysis
# ───────────────────────────────
coverage/
.scannerwork/

# ───────────────────────────────
# Environment variables
# ───────────────────────────────
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ───────────────────────────────
# Logs
# ───────────────────────────────
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ───────────────────────────────
# IDE/Editor settings
# ───────────────────────────────
.vscode/

# ───────────────────────────────
# Miscellaneous
# ───────────────────────────────
.DS_Store
