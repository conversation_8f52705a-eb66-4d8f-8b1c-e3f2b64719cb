<div align="center">

# 🚀 MeMate - Business Management Software

[![React](https://img.shields.io/badge/React-18.3.1-blue.svg)](https://reactjs.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](CONTRIBUTING.md)

<p align="center">
  <img src="public/logo.svg" alt="MeMate Logo" width="200"/>
</p>

**A comprehensive business management solution to streamline your operations**

[Features](#features) • [Installation](#installation) • [Usage](#usage) • [Tech Stack](#tech-stack) • [Contributing](#contributing) • [License](#license)

</div>

## 📋 Overview

MeMate is a powerful business management software designed to help businesses manage their operations efficiently. From client management to invoicing, expenses tracking to supplier management, MeMate provides a comprehensive suite of tools to streamline your business processes.

## ✨ Features

- **Dashboard** - Get a quick overview of your business performance
- **Client Management** - Manage your clients and their information
- **Supplier Management** - Keep track of your suppliers and their details
- **Expense Tracking** - Monitor and manage your business expenses
- **Invoicing** - Create and manage professional invoices
- **Quotations** - Generate and send quotations to clients
- **Regional Settings** - Customize the application based on your region
- **User Authentication** - Secure login and user management
- **Responsive Design** - Works seamlessly across devices

## 🛠️ Installation

### Prerequisites

- Node.js (v18.12 or higher)
- npm (v8 or higher)

### Setup

1. Clone the repository
   ```bash
   git clone https://github.com/yourusername/memate.git
   cd memate
   ```

2. Install dependencies
   ```bash
   npm install --legacy-peer-deps
   ```

3. Set up environment variables
   - Create a `.env` file based on `.env.dev` or `.env.app`
   - Configure the necessary API endpoints and keys

4. Start the development server
   ```bash
   npm start
   ```

### Docker Setup

Alternatively, you can use Docker to run the application:

```bash
docker-compose up -d
```

## 🚀 Usage

Once the application is running, you can access it at [http://localhost:3000](http://localhost:3000).

### Available Scripts

- `npm start` - Runs the app in development mode
- `npm run build` - Builds the app for production
- `npm test` - Launches the test runner
- `npm run eject` - Ejects from Create React App
- `npm run electron:serve` - Runs the app in Electron for desktop usage
- `npm run electron:build` - Builds the Electron app for distribution
- `npm run lint` - Runs ESLint to check code quality
- `npm run lint:fix` - Automatically fixes ESLint issues

## 💻 Tech Stack

### Frontend
- **React** - UI library
- **React Router** - Navigation
- **Material UI** - Component library
- **PrimeReact** - UI component framework
- **React Query** - Data fetching and state management
- **React Hook Form** - Form handling
- **Axios** - HTTP client
- **Chart.js** - Data visualization
- **SCSS/CSS** - Styling

### Development Tools
- **ESLint** - Code linting
- **Docker** - Containerization
- **Electron** - Desktop application framework

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgements

- [React](https://reactjs.org/)
- [Material UI](https://mui.com/)
- [PrimeReact](https://primereact.org/)
- [Create React App](https://create-react-app.dev/)

---

<div align="center">

**Made with ❤️ by the MeMate Team**

</div>